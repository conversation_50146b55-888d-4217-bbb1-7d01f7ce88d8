<?php
/**
 * OVHcloud OpenVPN Configuration Fix
 * Fixes VPN profile validation error: 2132017646
 */

require_once 'includes/config.php';

echo "<h1>☁️ OVHcloud OpenVPN Configuration Fix</h1>\n";
echo "<p>Fix the VPN profile validation error for your OVHcloud server</p>\n";
echo "<hr>\n";

echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
echo "<h4>❌ Current Issue Analysis</h4>\n";
echo "<p><strong>Error:</strong> VPN profile validation failed: 2132017646</p>\n";
echo "<p><strong>Cause:</strong> Android VPN service requires specific OpenVPN configuration format</p>\n";
echo "<p><strong>Solution:</strong> Convert your OVHcloud config to Android-compatible format</p>\n";
echo "</div>\n";

echo "<h2>Step 1: Get Your OVHcloud OpenVPN Configuration</h2>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>\n";
echo "<h4>📋 Required Information from OVHcloud</h4>\n";
echo "<p>You need to get the following from your OVHcloud OpenVPN server:</p>\n";
echo "<ol>\n";
echo "<li><strong>Server IP address</strong> (e.g., 51.68.xxx.xxx)</li>\n";
echo "<li><strong>Server port</strong> (usually 1194)</li>\n";
echo "<li><strong>CA certificate</strong> (ca.crt)</li>\n";
echo "<li><strong>Client certificate</strong> (client.crt)</li>\n";
echo "<li><strong>Client private key</strong> (client.key)</li>\n";
echo "<li><strong>Username and password</strong> (if using user/pass auth)</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h2>Step 2: Configure Your OVHcloud Server</h2>\n";

echo "<form method='post' style='margin: 20px 0;'>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
echo "<h4>🔧 OVHcloud Server Configuration</h4>\n";

echo "<table style='width: 100%; margin: 10px 0;'>\n";
echo "<tr>\n";
echo "<td style='padding: 5px; width: 150px;'><strong>Server IP:</strong></td>\n";
echo "<td style='padding: 5px;'><input type='text' name='server_ip' placeholder='51.68.xxx.xxx' style='width: 200px; padding: 5px;' required></td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td style='padding: 5px;'><strong>Server Port:</strong></td>\n";
echo "<td style='padding: 5px;'><input type='number' name='server_port' value='1194' style='width: 100px; padding: 5px;' required></td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td style='padding: 5px;'><strong>Username:</strong></td>\n";
echo "<td style='padding: 5px;'><input type='text' name='username' placeholder='your-username' style='width: 200px; padding: 5px;' required></td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td style='padding: 5px;'><strong>Password:</strong></td>\n";
echo "<td style='padding: 5px;'><input type='password' name='password' placeholder='your-password' style='width: 200px; padding: 5px;' required></td>\n";
echo "</tr>\n";
echo "</table>\n";

echo "<h4>📜 Certificates (Paste the content including BEGIN/END lines)</h4>\n";

echo "<p><strong>CA Certificate:</strong></p>\n";
echo "<textarea name='ca_cert' rows='8' style='width: 100%; font-family: monospace; font-size: 12px;' placeholder='-----BEGIN CERTIFICATE-----\nMIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF\n...\n-----END CERTIFICATE-----' required></textarea>\n";

echo "<p><strong>Client Certificate:</strong></p>\n";
echo "<textarea name='client_cert' rows='8' style='width: 100%; font-family: monospace; font-size: 12px;' placeholder='-----BEGIN CERTIFICATE-----\nMIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF\n...\n-----END CERTIFICATE-----' required></textarea>\n";

echo "<p><strong>Client Private Key:</strong></p>\n";
echo "<textarea name='client_key' rows='8' style='width: 100%; font-family: monospace; font-size: 12px;' placeholder='-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCydIBxynj\n...\n-----END PRIVATE KEY-----' required></textarea>\n";

echo "<br><br>\n";
echo "<button type='submit' name='configure_ovhcloud' style='background: #007cba; color: white; padding: 12px 24px; border: none; border-radius: 4px; cursor: pointer; font-size: 16px;'>Configure OVHcloud Server</button>\n";
echo "</div>\n";
echo "</form>\n";

// Handle the form submission
if (isset($_POST['configure_ovhcloud'])) {
    try {
        $serverIp = trim($_POST['server_ip']);
        $serverPort = (int)$_POST['server_port'];
        $username = trim($_POST['username']);
        $password = trim($_POST['password']);
        $caCert = trim($_POST['ca_cert']);
        $clientCert = trim($_POST['client_cert']);
        $clientKey = trim($_POST['client_key']);
        
        // Validate inputs
        if (empty($serverIp) || empty($username) || empty($password) || empty($caCert) || empty($clientCert) || empty($clientKey)) {
            throw new Exception("All fields are required");
        }
        
        // Create Android-compatible OpenVPN configuration
        $androidConfig = "client
dev tun
proto udp
remote $serverIp $serverPort
resolv-retry infinite
nobind
persist-key
persist-tun
cipher AES-256-CBC
auth SHA256
key-direction 1
verb 3
mute 20
auth-user-pass

# DNS Configuration for internet browsing
dhcp-option DNS *******
dhcp-option DNS *******

# Routing configuration
redirect-gateway def1 bypass-dhcp

# Connection settings
keepalive 10 120
ping-timer-rem
persist-tun
persist-key

# Inline certificates (required for Android VPN service)
<ca>
$caCert
</ca>

<cert>
$clientCert
</cert>

<key>
$clientKey
</key>";

        // Update the Working Test Server
        $updateSql = "UPDATE servers SET 
            configFile = ?, 
            username = ?, 
            password = ?
            WHERE name = 'Working Test Server'";
        $stmt = $conn->prepare($updateSql);
        $stmt->bind_param('sss', $androidConfig, $username, $password);
        
        if ($stmt->execute()) {
            echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
            echo "<h4>✅ OVHcloud Server Configured Successfully!</h4>\n";
            echo "<p><strong>Configuration Details:</strong></p>\n";
            echo "<ul>\n";
            echo "<li>Server: $serverIp:$serverPort</li>\n";
            echo "<li>Username: $username</li>\n";
            echo "<li>Format: Android-compatible OpenVPN</li>\n";
            echo "<li>Certificates: Inline format (required for Android)</li>\n";
            echo "</ul>\n";
            echo "<p><strong>Next Steps:</strong></p>\n";
            echo "<ol>\n";
            echo "<li>Test the Android app again</li>\n";
            echo "<li>The VPN profile validation error should be resolved</li>\n";
            echo "<li>You should see successful connection and internet browsing</li>\n";
            echo "</ol>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
            echo "<h4>❌ Database Update Failed</h4>\n";
            echo "<p>Error: " . $conn->error . "</p>\n";
            echo "</div>\n";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
        echo "<h4>❌ Configuration Error</h4>\n";
        echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
}

echo "<h2>Step 3: Alternative - Use Certificate Files</h2>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>\n";
echo "<h4>⚠️ Alternative Method</h4>\n";
echo "<p>If you have separate certificate files (.crt, .key), you can also:</p>\n";
echo "<ol>\n";
echo "<li>Upload the certificate files to your server</li>\n";
echo "<li>Reference them in the OpenVPN config using file paths</li>\n";
echo "<li>However, inline certificates (above method) work better with Android</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h2>Step 4: Troubleshooting</h2>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>\n";
echo "<h4>🔧 Common Issues and Solutions</h4>\n";
echo "<table border='1' cellpadding='8' cellspacing='0' style='width: 100%; border-collapse: collapse;'>\n";
echo "<tr style='background: #f8f9fa;'><th>Issue</th><th>Solution</th></tr>\n";
echo "<tr>\n";
echo "<td>VPN profile validation failed</td>\n";
echo "<td>Use inline certificates format (above method)</td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>Connection timeout</td>\n";
echo "<td>Check server IP and port, ensure server is running</td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>Authentication failed</td>\n";
echo "<td>Verify username/password and certificates</td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>Connected but no internet</td>\n";
echo "<td>Check DNS and routing configuration (should be fixed with our config)</td>\n";
echo "</tr>\n";
echo "</table>\n";
echo "</div>\n";

echo "<h2>Step 5: Test Your Configuration</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
echo "<h4>🧪 Testing Checklist</h4>\n";
echo "<ol>\n";
echo "<li><strong>Configure server</strong> using the form above</li>\n";
echo "<li><strong>Test Android app</strong> - should connect without validation error</li>\n";
echo "<li><strong>Check traffic stats</strong> - both upload and download should increase</li>\n";
echo "<li><strong>Test internet browsing</strong> - websites should load through VPN</li>\n";
echo "<li><strong>Verify IP change</strong> - your IP should show as the VPN server IP</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<p><strong>Navigation:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='validate_vpn_servers.php'>🔍 Validate Server Configurations</a></li>\n";
echo "<li><a href='test_vpn_connection_fixes.php'>🧪 Test API Endpoints</a></li>\n";
echo "<li><a href='vpn_fixes_summary.php'>📋 View Complete Fix Summary</a></li>\n";
echo "<li><a href='index.php'>← Back to Admin Panel</a></li>\n";
echo "</ul>\n";
?>
