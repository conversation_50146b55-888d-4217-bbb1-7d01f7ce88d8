<?php
/**
 * Fix Missing Flag Image
 * Creates placeholder flag images for test servers
 */

require_once 'includes/config.php';

echo "<h1>🏳️ Fix Missing Flag Images</h1>\n";
echo "<p>Creating placeholder flag images for test servers</p>\n";
echo "<hr>\n";

// Create flag directory if it doesn't exist
$flagDir = __DIR__ . '/flag';
if (!is_dir($flagDir)) {
    if (mkdir($flagDir, 0755, true)) {
        echo "<p style='color: #28a745;'>✅ Created flag directory: $flagDir</p>\n";
    } else {
        echo "<p style='color: #dc3545;'>❌ Failed to create flag directory</p>\n";
        exit;
    }
} else {
    echo "<p style='color: #17a2b8;'>ℹ️ Flag directory already exists</p>\n";
}

// Create a simple SVG placeholder for test_working.png
$svgContent = '<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" xmlns="http://www.w3.org/2000/svg">
  <rect width="64" height="64" fill="#28a745"/>
  <circle cx="32" cy="32" r="20" fill="#ffffff"/>
  <text x="32" y="38" text-anchor="middle" fill="#28a745" font-family="Arial" font-size="12" font-weight="bold">TEST</text>
</svg>';

// Save as SVG first
$svgPath = $flagDir . '/test_working.svg';
if (file_put_contents($svgPath, $svgContent)) {
    echo "<p style='color: #28a745;'>✅ Created SVG flag: test_working.svg</p>\n";
} else {
    echo "<p style='color: #dc3545;'>❌ Failed to create SVG flag</p>\n";
}

// Try to convert to PNG if ImageMagick is available
if (extension_loaded('imagick')) {
    try {
        $imagick = new Imagick();
        $imagick->readImageBlob($svgContent);
        $imagick->setImageFormat('png');
        $imagick->resizeImage(64, 64, Imagick::FILTER_LANCZOS, 1);
        
        $pngPath = $flagDir . '/test_working.png';
        if ($imagick->writeImage($pngPath)) {
            echo "<p style='color: #28a745;'>✅ Created PNG flag: test_working.png</p>\n";
        } else {
            echo "<p style='color: #ffc107;'>⚠️ Failed to create PNG flag</p>\n";
        }
        $imagick->clear();
    } catch (Exception $e) {
        echo "<p style='color: #ffc107;'>⚠️ ImageMagick error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
} else {
    echo "<p style='color: #ffc107;'>⚠️ ImageMagick not available, PNG conversion skipped</p>\n";
}

// Create a simple HTML-based PNG using GD if available
if (extension_loaded('gd')) {
    try {
        $image = imagecreate(64, 64);
        $green = imagecolorallocate($image, 40, 167, 69);  // Bootstrap success color
        $white = imagecolorallocate($image, 255, 255, 255);
        
        // Fill background
        imagefill($image, 0, 0, $green);
        
        // Draw circle
        imagefilledellipse($image, 32, 32, 40, 40, $white);
        
        // Add text
        $font = 3; // Built-in font
        $text = "TEST";
        $textWidth = imagefontwidth($font) * strlen($text);
        $textHeight = imagefontheight($font);
        $x = (64 - $textWidth) / 2;
        $y = (64 - $textHeight) / 2;
        imagestring($image, $font, $x, $y, $text, $green);
        
        $pngPath = $flagDir . '/test_working.png';
        if (imagepng($image, $pngPath)) {
            echo "<p style='color: #28a745;'>✅ Created PNG flag using GD: test_working.png</p>\n";
        } else {
            echo "<p style='color: #ffc107;'>⚠️ Failed to create PNG using GD</p>\n";
        }
        imagedestroy($image);
    } catch (Exception $e) {
        echo "<p style='color: #ffc107;'>⚠️ GD error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
} else {
    echo "<p style='color: #ffc107;'>⚠️ GD extension not available</p>\n";
}

// Update database to use SVG if PNG creation failed
$flagFile = 'test_working.png';
if (!file_exists($flagDir . '/test_working.png') && file_exists($flagDir . '/test_working.svg')) {
    $flagFile = 'test_working.svg';
    echo "<p style='color: #17a2b8;'>ℹ️ Using SVG flag as fallback</p>\n";
}

// Update the database
try {
    $updateSql = "UPDATE servers SET flagURL = ? WHERE name = 'Working Test Server'";
    $stmt = $conn->prepare($updateSql);
    $stmt->bind_param('s', $flagFile);
    
    if ($stmt->execute()) {
        echo "<p style='color: #28a745;'>✅ Updated database with flag: $flagFile</p>\n";
    } else {
        echo "<p style='color: #dc3545;'>❌ Failed to update database: " . $conn->error . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

// Test the flag URL
$flagUrl = "http://192.168.0.106/Svpn5g/5GsmartvpnAdminPanel/admin_new/flag/$flagFile";
echo "<h3>Flag URL Test</h3>\n";
echo "<p>Testing flag URL: <a href='$flagUrl' target='_blank'>$flagUrl</a></p>\n";

// Try to load the flag
$headers = @get_headers($flagUrl);
if ($headers && strpos($headers[0], '200') !== false) {
    echo "<p style='color: #28a745;'>✅ Flag URL is accessible</p>\n";
    echo "<img src='$flagUrl' alt='Test Flag' style='border: 1px solid #ccc; margin: 10px 0;'>\n";
} else {
    echo "<p style='color: #ffc107;'>⚠️ Flag URL may not be accessible from web</p>\n";
}

echo "<hr>\n";
echo "<h3>Summary</h3>\n";
echo "<p>Flag image issue should now be resolved. The Android app will no longer show the 404 error for the flag image.</p>\n";

echo "<p><strong>Navigation:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='setup_local_openvpn.php'>🖥️ Set Up Local OpenVPN Server</a></li>\n";
echo "<li><a href='validate_vpn_servers.php'>🔍 Validate Server Configurations</a></li>\n";
echo "<li><a href='index.php'>← Back to Admin Panel</a></li>\n";
echo "</ul>\n";
?>
