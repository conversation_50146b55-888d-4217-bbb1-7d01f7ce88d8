<?php
/**
 * Test VPN Connection Fixes
 * This script tests the API endpoints and server configurations
 */

require_once 'includes/config.php';

echo "<h2>VPN Connection Fixes - Test Results</h2>\n";
echo "<p>Testing API endpoints and server configurations...</p>\n";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>\n";
try {
    $result = $conn->query("SELECT COUNT(*) as count FROM servers");
    if ($result) {
        $row = $result->fetch_assoc();
        echo "<p style='color: green;'>✓ Database connection successful</p>\n";
        echo "<p>Total servers in database: <strong>" . $row['count'] . "</strong></p>\n";
    } else {
        echo "<p style='color: red;'>✗ Database query failed: " . $conn->error . "</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>\n";
}

// Test 2: Servers API Endpoint
echo "<h3>2. Servers API Endpoint Test</h3>\n";
try {
    // Test the servers API endpoint
    $timestamp = time();
    $signature = hash_hmac('sha256', $timestamp, API_KEY);
    $url = ADMIN_PANEL_URL . "api/servers.php?status=1&timestamp=$timestamp&signature=$signature";
    
    echo "<p>Testing URL: <code>" . htmlspecialchars($url) . "</code></p>\n";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        
        if ($data && isset($data['success']) && $data['success']) {
            echo "<p style='color: green;'>✓ Servers API endpoint working</p>\n";
            echo "<p>Active servers found: <strong>" . count($data['servers']) . "</strong></p>\n";
            
            // Display server details
            if (!empty($data['servers'])) {
                echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
                echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Status</th></tr>\n";
                
                foreach ($data['servers'] as $server) {
                    $typeText = $server['type'] == 1 ? 'Free' : 'Premium';
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($server['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($server['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($typeText) . "</td>";
                    echo "<td>" . ($server['status'] == 1 ? 'Active' : 'Inactive') . "</td>";
                    echo "</tr>\n";
                }
                
                echo "</table>\n";
            }
        } else {
            echo "<p style='color: red;'>✗ API returned error: " . htmlspecialchars($response) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ Failed to fetch from servers API</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Servers API test failed: " . $e->getMessage() . "</p>\n";
}

// Test 3: Server Configuration Validation
echo "<h3>3. Server Configuration Validation</h3>\n";
try {
    $result = $conn->query("SELECT id, name, configFile, username, password FROM servers WHERE status = 1");
    
    if ($result && $result->num_rows > 0) {
        $validServers = 0;
        $invalidServers = 0;
        
        while ($row = $result->fetch_assoc()) {
            $isValid = true;
            $issues = [];
            
            // Check if config contains essential directives
            if (empty($row['configFile']) || !str_contains($row['configFile'], 'client')) {
                $isValid = false;
                $issues[] = "Missing 'client' directive";
            }
            
            if (empty($row['configFile']) || !str_contains($row['configFile'], 'remote')) {
                $isValid = false;
                $issues[] = "Missing 'remote' directive";
            }
            
            if (empty($row['username'])) {
                $isValid = false;
                $issues[] = "Missing username";
            }
            
            if (empty($row['password'])) {
                $isValid = false;
                $issues[] = "Missing password";
            }
            
            if ($isValid) {
                $validServers++;
                echo "<p style='color: green;'>✓ " . htmlspecialchars($row['name']) . " - Configuration valid</p>\n";
            } else {
                $invalidServers++;
                echo "<p style='color: orange;'>⚠ " . htmlspecialchars($row['name']) . " - Issues: " . implode(', ', $issues) . "</p>\n";
            }
        }
        
        echo "<hr>\n";
        echo "<p>Valid servers: <strong style='color: green;'>$validServers</strong></p>\n";
        echo "<p>Invalid servers: <strong style='color: orange;'>$invalidServers</strong></p>\n";
        
    } else {
        echo "<p style='color: red;'>✗ No active servers found</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Configuration validation failed: " . $e->getMessage() . "</p>\n";
}

// Test 4: Custom Ads API
echo "<h3>4. Custom Ads API Test</h3>\n";
try {
    $timestamp = time();
    $signature = hash_hmac('sha256', $timestamp, API_KEY);
    $url = ADMIN_PANEL_URL . "api/custom_ads.php?timestamp=$timestamp&signature=$signature";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET'
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    
    if ($response !== false) {
        $data = json_decode($response, true);
        
        if ($data && is_array($data)) {
            echo "<p style='color: green;'>✓ Custom Ads API working</p>\n";
            echo "<p>Active custom ads: <strong>" . count($data) . "</strong></p>\n";
        } else {
            echo "<p style='color: orange;'>⚠ Custom Ads API returned: " . htmlspecialchars($response) . "</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ Failed to fetch from Custom Ads API</p>\n";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Custom Ads API test failed: " . $e->getMessage() . "</p>\n";
}

// Test 5: API Authentication
echo "<h3>5. API Authentication Test</h3>\n";
try {
    // Test with invalid signature
    $timestamp = time();
    $invalidSignature = 'invalid_signature';
    $url = ADMIN_PANEL_URL . "api/servers.php?status=1&timestamp=$timestamp&signature=$invalidSignature";
    
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'method' => 'GET',
            'ignore_errors' => true
        ]
    ]);
    
    $response = file_get_contents($url, false, $context);
    $httpCode = null;
    
    if (isset($http_response_header)) {
        foreach ($http_response_header as $header) {
            if (strpos($header, 'HTTP/') === 0) {
                $httpCode = (int) substr($header, 9, 3);
                break;
            }
        }
    }
    
    if ($httpCode === 401) {
        echo "<p style='color: green;'>✓ API authentication working (correctly rejected invalid signature)</p>\n";
    } else {
        echo "<p style='color: orange;'>⚠ API authentication may have issues (HTTP code: $httpCode)</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ API authentication test failed: " . $e->getMessage() . "</p>\n";
}

// Test 6: VPN Connection Diagnosis
echo "<h3>6. VPN Connection Diagnosis</h3>\n";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>\n";
echo "<h4>🔍 VPN Connection Issue Analysis</h4>\n";
echo "<p><strong>Problem:</strong> VPN shows connected but no internet browsing (0 download bytes)</p>\n";
echo "<p><strong>Symptoms:</strong></p>\n";
echo "<ul>\n";
echo "<li>✅ VPN connection appears successful</li>\n";
echo "<li>✅ Upload traffic is being tracked</li>\n";
echo "<li>❌ Download traffic remains at 0 bytes</li>\n";
echo "<li>❌ No websites can be accessed through VPN</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<h4>Root Cause Analysis:</h4>\n";
echo "<ol>\n";
echo "<li><strong>Mock OpenVPN Implementation:</strong> The SimpleOpenVPNClient uses basic packet forwarding without proper OpenVPN protocol</li>\n";
echo "<li><strong>Invalid Server Endpoints:</strong> Test servers point to non-functional OpenVPN servers</li>\n";
echo "<li><strong>Missing Server-Side Processing:</strong> No actual OpenVPN server to handle connections</li>\n";
echo "<li><strong>Incomplete Packet Routing:</strong> Packets sent but responses not properly routed back</li>\n";
echo "</ol>\n";

echo "<h4>Recommended Fixes:</h4>\n";
echo "<div style='background: #f0fff0; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
echo "<p><strong>Immediate Actions:</strong></p>\n";
echo "<ol>\n";
echo "<li>Set up a local OpenVPN server for testing</li>\n";
echo "<li>Update server configurations with working endpoints</li>\n";
echo "<li>Improve Android VPN client implementation</li>\n";
echo "<li>Add proper DNS and routing configuration</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<hr>\n";
echo "<h3>Summary</h3>\n";
echo "<p>Test completed. Review the results above to ensure all components are working correctly.</p>\n";
echo "<p><strong>Next Steps:</strong></p>\n";
echo "<ul>\n";
echo "<li>If servers are missing, run the <a href='run_server_migration.php'>Server Migration</a></li>\n";
echo "<li>If API endpoints fail, check the database connection and API key configuration</li>\n";
echo "<li>If server configurations are invalid, update them in the admin panel</li>\n";
echo "<li><strong>🔧 <a href='fix_vpn_connection_advanced.php' style='color: #007cba; font-weight: bold;'>Run Advanced VPN Connection Fixes</a></strong></li>\n";
echo "</ul>\n";

echo "<p><a href='index.php'>← Back to Admin Panel</a></p>\n";
?>
