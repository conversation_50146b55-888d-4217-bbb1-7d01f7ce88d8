<?php
/**
 * VPN Server Validation Script
 * Phase 1.3: Server Configuration Testing and Validation
 * 
 * This script validates server configurations and tests connectivity
 * to identify issues causing "VPN connects but no internet browsing"
 */

require_once 'includes/config.php';

echo "<h1>🔍 VPN Server Validation</h1>\n";
echo "<p>Testing server configurations and connectivity...</p>\n";
echo "<hr>\n";

/**
 * Validate OpenVPN configuration
 */
function validateOpenVPNConfig($config) {
    $issues = [];
    $warnings = [];
    
    // Essential directives check
    $essentialDirectives = [
        'client' => 'Client mode directive',
        'dev tun' => 'TUN device specification',
        'proto' => 'Protocol specification',
        'remote' => 'Remote server specification'
    ];
    
    foreach ($essentialDirectives as $directive => $description) {
        if (!str_contains($config, $directive)) {
            $issues[] = "Missing: $description ($directive)";
        }
    }
    
    // DNS configuration check
    if (!str_contains($config, 'dhcp-option DNS')) {
        $warnings[] = "No DNS servers specified - may cause browsing issues";
    }
    
    // Route redirection check
    if (!str_contains($config, 'redirect-gateway') && !str_contains($config, 'pull')) {
        $warnings[] = "No route redirection - traffic may not go through VPN";
    }
    
    // Security settings check
    if (!str_contains($config, 'auth') && !str_contains($config, 'cipher')) {
        $warnings[] = "No encryption settings specified";
    }
    
    // Test endpoints check
    if (str_contains($config, '*******') || str_contains($config, '127.0.0.1')) {
        $issues[] = "Using test/invalid endpoints (******* or 127.0.0.1)";
    }
    
    return [
        'valid' => empty($issues),
        'issues' => $issues,
        'warnings' => $warnings
    ];
}

/**
 * Test server connectivity
 */
function testServerConnectivity($host, $port = 1194) {
    $timeout = 5;
    
    // Test UDP connectivity (OpenVPN default)
    $socket = @socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);
    if (!$socket) {
        return ['reachable' => false, 'error' => 'Failed to create socket'];
    }
    
    socket_set_option($socket, SOL_SOCKET, SO_RCVTIMEO, ['sec' => $timeout, 'usec' => 0]);
    socket_set_option($socket, SOL_SOCKET, SO_SNDTIMEO, ['sec' => $timeout, 'usec' => 0]);
    
    $result = @socket_connect($socket, $host, $port);
    socket_close($socket);
    
    if ($result) {
        return ['reachable' => true, 'method' => 'UDP'];
    }
    
    // Test TCP connectivity as fallback
    $connection = @fsockopen($host, $port, $errno, $errstr, $timeout);
    if ($connection) {
        fclose($connection);
        return ['reachable' => true, 'method' => 'TCP'];
    }
    
    return ['reachable' => false, 'error' => "Connection failed: $errstr ($errno)"];
}

try {
    // Get all active servers
    $result = $conn->query("SELECT id, name, username, password, configFile FROM servers WHERE status = 1 ORDER BY pos ASC");
    
    if (!$result || $result->num_rows === 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
        echo "<h4>❌ No Active Servers Found</h4>\n";
        echo "<p>No active servers found in the database. Please add servers first.</p>\n";
        echo "</div>\n";
        exit;
    }
    
    echo "<h2>Server Configuration Validation Results</h2>\n";
    echo "<table border='1' cellpadding='8' cellspacing='0' style='width: 100%; border-collapse: collapse;'>\n";
    echo "<tr style='background: #f8f9fa;'>\n";
    echo "<th>Server</th><th>Config Status</th><th>Connectivity</th><th>Issues</th><th>Recommendations</th>\n";
    echo "</tr>\n";
    
    $totalServers = 0;
    $validServers = 0;
    $reachableServers = 0;
    
    while ($row = $result->fetch_assoc()) {
        $totalServers++;
        
        echo "<tr>\n";
        echo "<td><strong>" . htmlspecialchars($row['name']) . "</strong><br>\n";
        echo "<small>ID: " . $row['id'] . " | User: " . htmlspecialchars($row['username']) . "</small></td>\n";
        
        // Validate configuration
        $validation = validateOpenVPNConfig($row['configFile']);
        $configStatus = $validation['valid'] ? 
            "<span style='color: #28a745; font-weight: bold;'>✅ Valid</span>" : 
            "<span style='color: #dc3545; font-weight: bold;'>❌ Invalid</span>";
        
        if ($validation['valid']) $validServers++;
        
        echo "<td>$configStatus</td>\n";
        
        // Test connectivity
        $connectivityResult = ['reachable' => false, 'error' => 'No remote host found'];
        
        // Extract remote host from config
        if (preg_match('/remote\s+([^\s]+)\s+(\d+)/', $row['configFile'], $matches)) {
            $host = $matches[1];
            $port = isset($matches[2]) ? (int)$matches[2] : 1194;
            $connectivityResult = testServerConnectivity($host, $port);
        }
        
        $connectivityStatus = $connectivityResult['reachable'] ? 
            "<span style='color: #28a745; font-weight: bold;'>✅ Reachable</span><br><small>" . $connectivityResult['method'] . "</small>" : 
            "<span style='color: #dc3545; font-weight: bold;'>❌ Unreachable</span><br><small>" . $connectivityResult['error'] . "</small>";
        
        if ($connectivityResult['reachable']) $reachableServers++;
        
        echo "<td>$connectivityStatus</td>\n";
        
        // Display issues
        $allIssues = array_merge($validation['issues'], $validation['warnings']);
        echo "<td>" . (empty($allIssues) ? "<span style='color: #28a745;'>None</span>" : implode('<br>', $allIssues)) . "</td>\n";
        
        // Recommendations
        $recommendations = [];
        if (!$validation['valid']) {
            $recommendations[] = "Fix configuration issues";
        }
        if (!$connectivityResult['reachable']) {
            $recommendations[] = "Check server availability";
        }
        if (empty($validation['issues']) && empty($validation['warnings']) && $connectivityResult['reachable']) {
            $recommendations[] = "<span style='color: #28a745;'>Ready for testing</span>";
        }
        
        echo "<td>" . implode('<br>', $recommendations) . "</td>\n";
        echo "</tr>\n";
    }
    
    echo "</table>\n";
    
    // Summary
    echo "<hr>\n";
    echo "<h3>Validation Summary</h3>\n";
    echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
    echo "<p><strong>Total Servers:</strong> $totalServers</p>\n";
    echo "<p><strong>Valid Configurations:</strong> <span style='color: " . ($validServers > 0 ? '#28a745' : '#dc3545') . ";'>$validServers</span></p>\n";
    echo "<p><strong>Reachable Servers:</strong> <span style='color: " . ($reachableServers > 0 ? '#28a745' : '#dc3545') . ";'>$reachableServers</span></p>\n";
    echo "</div>\n";
    
    // Recommendations based on results
    echo "<h3>Recommendations</h3>\n";
    
    if ($validServers === 0) {
        echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
        echo "<h4>🚨 Critical: No Valid Server Configurations</h4>\n";
        echo "<p>All server configurations have issues. This explains why VPN connects but doesn't work.</p>\n";
        echo "<p><strong>Action Required:</strong> <a href='fix_vpn_connection_advanced.php'>Run Advanced VPN Fixes</a></p>\n";
        echo "</div>\n";
    } elseif ($reachableServers === 0) {
        echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>\n";
        echo "<h4>⚠️ Warning: No Reachable Servers</h4>\n";
        echo "<p>Server configurations are valid but servers are not reachable.</p>\n";
        echo "<p><strong>Action Required:</strong> Set up local OpenVPN server or use working endpoints</p>\n";
        echo "</div>\n";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
        echo "<h4>✅ Good: Some Servers Are Ready</h4>\n";
        echo "<p>You have $reachableServers reachable server(s) with valid configurations.</p>\n";
        echo "<p><strong>Next Step:</strong> Test VPN connection with these servers</p>\n";
        echo "</div>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545; font-weight: bold;'>❌ Validation Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><strong>Navigation:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='fix_vpn_connection_advanced.php'>🔧 Advanced VPN Fixes</a></li>\n";
echo "<li><a href='test_vpn_connection_fixes.php'>🧪 Test API Endpoints</a></li>\n";
echo "<li><a href='servers.php'>📋 Manage Servers</a></li>\n";
echo "<li><a href='index.php'>← Back to Admin Panel</a></li>\n";
echo "</ul>\n";
?>
