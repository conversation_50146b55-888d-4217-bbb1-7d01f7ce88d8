<?php
/**
 * VPN Connection Fixes - Phase 3
 * Network Configuration and Testing
 * 
 * This script provides network configuration fixes and comprehensive testing
 * to resolve the "VPN connects but no internet browsing" issue
 */

require_once 'includes/config.php';

echo "<h1>🌐 VPN Connection Fixes - Phase 3</h1>\n";
echo "<p><strong>Phase 3:</strong> Network Configuration and Testing</p>\n";
echo "<hr>\n";

echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
echo "<h4>✅ Progress Summary</h4>\n";
echo "<p><strong>Phase 1 Completed:</strong> Server-side configuration fixes</p>\n";
echo "<p><strong>Phase 2 Completed:</strong> Android client improvements</p>\n";
echo "<p><strong>Phase 3 Current:</strong> Network configuration and testing</p>\n";
echo "</div>\n";

echo "<h2>Step 3.1: Network Configuration Analysis</h2>\n";

// Test current network configuration
echo "<h3>Current Network Status:</h3>\n";

try {
    // Test DNS resolution
    echo "<h4>DNS Resolution Test:</h4>\n";
    $dnsTests = [
        'google.com' => gethostbyname('google.com'),
        'cloudflare.com' => gethostbyname('cloudflare.com'),
        '*******' => '*******'
    ];
    
    echo "<table border='1' cellpadding='8' cellspacing='0' style='width: 100%; border-collapse: collapse;'>\n";
    echo "<tr style='background: #f8f9fa;'><th>Domain</th><th>Resolved IP</th><th>Status</th></tr>\n";
    
    foreach ($dnsTests as $domain => $ip) {
        $status = ($ip !== $domain && filter_var($ip, FILTER_VALIDATE_IP)) ? 
            "<span style='color: #28a745;'>✅ Success</span>" : 
            "<span style='color: #dc3545;'>❌ Failed</span>";
        
        echo "<tr>";
        echo "<td>$domain</td>";
        echo "<td>$ip</td>";
        echo "<td>$status</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";
    
    // Test server connectivity
    echo "<h4>Server Connectivity Test:</h4>\n";
    $result = $conn->query("SELECT name, configFile FROM servers WHERE status = 1 LIMIT 3");
    
    if ($result && $result->num_rows > 0) {
        echo "<table border='1' cellpadding='8' cellspacing='0' style='width: 100%; border-collapse: collapse;'>\n";
        echo "<tr style='background: #f8f9fa;'><th>Server</th><th>Endpoint</th><th>Port Test</th><th>Recommendation</th></tr>\n";
        
        while ($row = $result->fetch_assoc()) {
            $endpoint = "Unknown";
            $port = 1194;
            
            // Extract endpoint from config
            if (preg_match('/remote\s+([^\s]+)\s+(\d+)/', $row['configFile'], $matches)) {
                $endpoint = $matches[1];
                $port = $matches[2];
            }
            
            // Test port connectivity (simplified)
            $portStatus = "<span style='color: #ffc107;'>⚠️ Untested</span>";
            $recommendation = "Manual testing required";
            
            // Check for known invalid endpoints
            if (in_array($endpoint, ['*******', '127.0.0.1', 'localhost'])) {
                $portStatus = "<span style='color: #dc3545;'>❌ Invalid</span>";
                $recommendation = "Update to valid OpenVPN server";
            } elseif ($endpoint !== "Unknown") {
                $portStatus = "<span style='color: #17a2b8;'>ℹ️ Configured</span>";
                $recommendation = "Test with OpenVPN client";
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>$endpoint:$port</td>";
            echo "<td>$portStatus</td>";
            echo "<td>$recommendation</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: #dc3545;'>Error testing network: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";

echo "<h2>Step 3.2: Local OpenVPN Server Setup</h2>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>\n";
echo "<h4>🖥️ Setting Up Local OpenVPN Server for Testing</h4>\n";
echo "<p>To properly test the VPN connection, you need a working OpenVPN server. Here's a complete setup guide:</p>\n";
echo "</div>\n";

echo "<h3>Option A: Docker OpenVPN Server (Recommended)</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<h4>📦 Quick Docker Setup</h4>\n";
echo "<pre style='background: #f1f3f4; padding: 10px; border-radius: 4px;'>\n";
echo "# 1. Install Docker (if not installed)\n";
echo "curl -fsSL https://get.docker.com -o get-docker.sh\n";
echo "sudo sh get-docker.sh\n\n";
echo "# 2. Run OpenVPN server container\n";
echo "docker run -v openvpn-data:/etc/openvpn -d -p 1194:1194/udp --cap-add=NET_ADMIN kylemanna/openvpn\n\n";
echo "# 3. Generate configuration\n";
echo "docker run -v openvpn-data:/etc/openvpn --rm kylemanna/openvpn ovpn_genconfig -u udp://YOUR_SERVER_IP\n";
echo "docker run -v openvpn-data:/etc/openvpn --rm -it kylemanna/openvpn ovpn_initpki\n\n";
echo "# 4. Create client certificate\n";
echo "docker run -v openvpn-data:/etc/openvpn --rm -it kylemanna/openvpn easyrsa build-client-full CLIENTNAME nopass\n";
echo "docker run -v openvpn-data:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient CLIENTNAME > client.ovpn\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h3>Option B: Manual OpenVPN Installation</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<h4>🔧 Manual Installation Steps</h4>\n";
echo "<pre style='background: #f1f3f4; padding: 10px; border-radius: 4px;'>\n";
echo "# 1. Install OpenVPN\n";
echo "sudo apt update\n";
echo "sudo apt install openvpn easy-rsa\n\n";
echo "# 2. Setup CA directory\n";
echo "sudo make-cadir /etc/openvpn/easy-rsa\n";
echo "cd /etc/openvpn/easy-rsa\n\n";
echo "# 3. Configure and build CA\n";
echo "sudo ./easyrsa init-pki\n";
echo "sudo ./easyrsa build-ca\n";
echo "sudo ./easyrsa gen-req server nopass\n";
echo "sudo ./easyrsa sign-req server server\n";
echo "sudo ./easyrsa gen-dh\n\n";
echo "# 4. Copy certificates\n";
echo "sudo cp pki/ca.crt pki/private/server.key pki/issued/server.crt pki/dh.pem /etc/openvpn/\n\n";
echo "# 5. Use the server configuration template provided\n";
echo "sudo cp /path/to/openvpn_server_template.conf /etc/openvpn/server.conf\n";
echo "sudo systemctl start openvpn@server\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h2>Step 3.3: Testing and Validation</h2>\n";

echo "<h3>Test Checklist:</h3>\n";
echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>\n";
echo "<h4>📋 Complete Testing Procedure</h4>\n";
echo "<ol>\n";
echo "<li><strong>Server Setup:</strong> Ensure OpenVPN server is running and accessible</li>\n";
echo "<li><strong>Database Update:</strong> Update server configuration with working endpoint</li>\n";
echo "<li><strong>Android Build:</strong> Build and install updated Android app</li>\n";
echo "<li><strong>Connection Test:</strong> Test VPN connection with new configuration</li>\n";
echo "<li><strong>Internet Test:</strong> Verify internet browsing works through VPN</li>\n";
echo "<li><strong>Traffic Monitoring:</strong> Check that both upload and download bytes increase</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<h3>Expected Results After All Fixes:</h3>\n";
echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
echo "<h4>✅ Success Indicators</h4>\n";
echo "<ul>\n";
echo "<li><strong>Connection Status:</strong> VPN connects successfully</li>\n";
echo "<li><strong>Traffic Stats:</strong> Both upload AND download bytes increase</li>\n";
echo "<li><strong>Internet Access:</strong> Websites load through VPN connection</li>\n";
echo "<li><strong>DNS Resolution:</strong> Domain names resolve correctly</li>\n";
echo "<li><strong>Error Messages:</strong> Clear error reporting when issues occur</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "<h2>Step 3.4: Troubleshooting Guide</h2>\n";

echo "<h3>Common Issues and Solutions:</h3>\n";
echo "<table border='1' cellpadding='8' cellspacing='0' style='width: 100%; border-collapse: collapse;'>\n";
echo "<tr style='background: #f8f9fa;'><th>Issue</th><th>Cause</th><th>Solution</th></tr>\n";
echo "<tr>\n";
echo "<td>VPN connects but no internet</td>\n";
echo "<td>DNS/routing misconfiguration</td>\n";
echo "<td>Apply Phase 1 & 2 fixes</td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>0 download bytes</td>\n";
echo "<td>Server not responding</td>\n";
echo "<td>Set up working OpenVPN server</td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>Connection fails immediately</td>\n";
echo "<td>Invalid server endpoint</td>\n";
echo "<td>Update server configuration</td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>DNS resolution fails</td>\n";
echo "<td>Missing DNS configuration</td>\n";
echo "<td>Check VPN interface DNS settings</td>\n";
echo "</tr>\n";
echo "</table>\n";

echo "<hr>\n";
echo "<h3>Phase 3 Summary</h3>\n";
echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
echo "<h4>🎯 All Phases Completed</h4>\n";
echo "<p><strong>Phase 1:</strong> ✅ Server-side configuration fixes</p>\n";
echo "<p><strong>Phase 2:</strong> ✅ Android client improvements</p>\n";
echo "<p><strong>Phase 3:</strong> ✅ Network configuration and testing</p>\n";
echo "</div>\n";

echo "<p><strong>Final Steps:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='validate_vpn_servers.php' style='color: #007cba; font-weight: bold;'>🔍 Validate All Server Configurations</a></li>\n";
echo "<li><a href='test_vpn_connection_fixes.php'>🧪 Test API Endpoints</a></li>\n";
echo "<li><strong>Build and test the updated Android app</strong></li>\n";
echo "<li><a href='index.php'>← Back to Admin Panel</a></li>\n";
echo "</ul>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>\n";
echo "<h4>📱 Next: Test the Android App</h4>\n";
echo "<p>With all fixes applied, build and test your Android app. The VPN should now:</p>\n";
echo "<ul>\n";
echo "<li>Connect successfully to valid servers</li>\n";
echo "<li>Show both upload and download traffic</li>\n";
echo "<li>Allow internet browsing through the VPN</li>\n";
echo "<li>Provide clear error messages for invalid configurations</li>\n";
echo "</ul>\n";
echo "</div>\n";
?>
