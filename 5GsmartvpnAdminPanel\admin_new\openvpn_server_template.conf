# OpenVPN Server Configuration Template
# For 5G Smart VPN Admin Panel
# This configuration addresses the "VPN connects but no internet" issue

# Server mode and protocol
port 1194
proto udp
dev tun

# SSL/TLS root certificate (ca), certificate (cert), and private key (key)
ca ca.crt
cert server.crt
key server.key
dh dh2048.pem

# Network topology
topology subnet

# Configure server mode and supply a VPN subnet
# for OpenVPN to draw client addresses from
server ******** *************

# Maintain a record of client <-> virtual IP address
# associations in this file
ifconfig-pool-persist ipp.txt

# Configure server mode for ethernet bridging
# You must first use your OS's bridging capability
# to bridge the TAP interface with the ethernet NIC interface
;server-bridge ******** ************* ********* **********

# Configure server mode for ethernet bridging
# using a DHCP-proxy, where clients talk
# to the OpenVPN server-side DHCP server
# to receive their IP address allocation
# and DNS server addresses
;server-bridge

# Push routes to the client to allow it
# to reach other private subnets behind
# the server. Remember that these
# private subnets will also need
# to know to route the OpenVPN client
# address pool (********/*************)
# back to the OpenVPN server
;push "route ************ *************"
;push "route ************ *************"

# To assign specific IP addresses to specific
# clients or if a connecting client has a private
# subnet behind it that should also have VPN access,
# use the subdirectory "ccd" for client-specific
# configuration files
;client-config-dir ccd
;route ************** ***************

# CRITICAL: Push DNS servers to clients
# This fixes the "no internet browsing" issue
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"
push "dhcp-option DNS *******"

# CRITICAL: Redirect all traffic through VPN
# This ensures all client traffic goes through the VPN tunnel
push "redirect-gateway def1 bypass-dhcp"

# The keepalive directive causes ping-like
# messages to be sent back and forth over
# the link so that each side knows when
# the other side has gone down
keepalive 10 120

# For extra security beyond that provided
# by SSL/TLS, create an "HMAC firewall"
# to help block DoS attacks and UDP port flooding
tls-auth ta.key 0 # This file is secret
key-direction 0

# Select a cryptographic cipher
cipher AES-256-CBC

# Enable compression on the VPN link and push the
# option to the client (v2.4+ only, for earlier
# versions see below)
;compress lz4-v2
;push "compress lz4-v2"

# For compression compatible with older clients use comp-lzo
# If you enable it here, you must also
# enable it in the client config file
comp-lzo

# The maximum number of concurrently connected clients
max-clients 100

# It's a good idea to reduce the OpenVPN
# daemon's privileges after initialization
user nobody
group nogroup

# The persist options will try to avoid
# accessing certain resources on restart
# that may no longer be accessible because
# of the privilege downgrade
persist-key
persist-tun

# Output a short status file showing
# current connections, truncated
# and rewritten every minute
status openvpn-status.log

# By default, log messages will go to the syslog (or
# on Windows, if running as a service, they will go to
# the "\Program Files\OpenVPN\log" directory)
# Use log or log-append to override this default
log         openvpn.log
log-append  openvpn.log

# Set the appropriate level of log
# file verbosity
#
# 0 is silent, except for fatal errors
# 4 is reasonable for general usage
# 5 and 6 can help to debug connection problems
# 9 is extremely verbose
verb 3

# Silence repeating messages
mute 20

# Notify the client that when the server restarts so it
# can automatically reconnect
explicit-exit-notify 1

# CUSTOM SETTINGS FOR ANDROID VPN APP
# These settings help resolve connection issues

# Allow multiple clients with the same certificate
duplicate-cn

# Enable client-to-client communication
client-to-client

# Script security level
script-security 2

# Management interface (for monitoring)
management localhost 7505

# Custom routing for better connectivity
# Push specific routes if needed
;push "route 0.0.0.0 0.0.0.0"

# MTU settings for better performance
tun-mtu 1500
fragment 1300
mssfix 1200

# Authentication settings
auth SHA256

# Renegotiation settings
reneg-sec 0

# Client certificate verification
verify-client-cert require
