<?php
/**
 * Local OpenVPN Server Setup Guide
 * Quick setup for testing VPN connection fixes
 */

require_once 'includes/config.php';

echo "<h1>🖥️ Local OpenVPN Server Setup</h1>\n";
echo "<p>Quick setup guide to create a working OpenVPN server for testing</p>\n";
echo "<hr>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>\n";
echo "<h4>⚠️ Current Issue Analysis</h4>\n";
echo "<p>From your logs, the VPN client is trying to connect to <code>vpn.example.com:1194</code> which doesn't exist.</p>\n";
echo "<p><strong>Solution:</strong> Set up a local OpenVPN server on <code>*************:1194</code></p>\n";
echo "</div>\n";

echo "<h2>Option 1: Quick Docker Setup (Recommended)</h2>\n";

echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
echo "<h4>🐳 Docker OpenVPN Server</h4>\n";
echo "<p>This is the fastest way to get a working OpenVPN server:</p>\n";
echo "</div>\n";

echo "<h3>Step 1: Install Docker</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<pre style='background: #f1f3f4; padding: 10px; border-radius: 4px;'>\n";
echo "# Install Docker on Ubuntu/Debian\n";
echo "curl -fsSL https://get.docker.com -o get-docker.sh\n";
echo "sudo sh get-docker.sh\n";
echo "sudo usermod -aG docker \$USER\n";
echo "# Log out and back in, or run: newgrp docker\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h3>Step 2: Run OpenVPN Server</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<pre style='background: #f1f3f4; padding: 10px; border-radius: 4px;'>\n";
echo "# Create volume for OpenVPN data\n";
echo "docker volume create --name openvpn-data\n\n";
echo "# Generate configuration (replace ************* with your server IP)\n";
echo "docker run -v openvpn-data:/etc/openvpn --rm kylemanna/openvpn ovpn_genconfig -u udp://*************\n\n";
echo "# Initialize PKI (you'll be prompted for a passphrase)\n";
echo "docker run -v openvpn-data:/etc/openvpn --rm -it kylemanna/openvpn ovpn_initpki\n\n";
echo "# Start the OpenVPN server\n";
echo "docker run -v openvpn-data:/etc/openvpn -d -p 1194:1194/udp --cap-add=NET_ADMIN --name openvpn-server kylemanna/openvpn\n\n";
echo "# Generate client certificate\n";
echo "docker run -v openvpn-data:/etc/openvpn --rm -it kylemanna/openvpn easyrsa build-client-full android-client nopass\n\n";
echo "# Get client configuration\n";
echo "docker run -v openvpn-data:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient android-client > android-client.ovpn\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h2>Option 2: Manual Installation</h2>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>\n";
echo "<h4>🔧 Manual OpenVPN Setup</h4>\n";
echo "<p>If you prefer manual installation:</p>\n";
echo "</div>\n";

echo "<h3>Step 1: Install OpenVPN</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<pre style='background: #f1f3f4; padding: 10px; border-radius: 4px;'>\n";
echo "# Ubuntu/Debian\n";
echo "sudo apt update\n";
echo "sudo apt install openvpn easy-rsa\n\n";
echo "# CentOS/RHEL\n";
echo "sudo yum install epel-release\n";
echo "sudo yum install openvpn easy-rsa\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h3>Step 2: Setup Certificates</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<pre style='background: #f1f3f4; padding: 10px; border-radius: 4px;'>\n";
echo "# Create CA directory\n";
echo "sudo make-cadir /etc/openvpn/easy-rsa\n";
echo "cd /etc/openvpn/easy-rsa\n\n";
echo "# Initialize PKI\n";
echo "sudo ./easyrsa init-pki\n";
echo "sudo ./easyrsa build-ca nopass\n";
echo "sudo ./easyrsa gen-req server nopass\n";
echo "sudo ./easyrsa sign-req server server\n";
echo "sudo ./easyrsa gen-dh\n\n";
echo "# Generate client certificate\n";
echo "sudo ./easyrsa gen-req android-client nopass\n";
echo "sudo ./easyrsa sign-req client android-client\n\n";
echo "# Copy files\n";
echo "sudo cp pki/ca.crt pki/private/server.key pki/issued/server.crt pki/dh.pem /etc/openvpn/\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h3>Step 3: Server Configuration</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<p>Create <code>/etc/openvpn/server.conf</code>:</p>\n";
echo "<pre style='background: #f1f3f4; padding: 10px; border-radius: 4px;'>\n";
echo "port 1194\n";
echo "proto udp\n";
echo "dev tun\n";
echo "ca ca.crt\n";
echo "cert server.crt\n";
echo "key server.key\n";
echo "dh dh.pem\n";
echo "server ******** *************\n";
echo "ifconfig-pool-persist ipp.txt\n";
echo "push \"redirect-gateway def1 bypass-dhcp\"\n";
echo "push \"dhcp-option DNS *******\"\n";
echo "push \"dhcp-option DNS *******\"\n";
echo "keepalive 10 120\n";
echo "cipher AES-256-CBC\n";
echo "auth SHA256\n";
echo "user nobody\n";
echo "group nogroup\n";
echo "persist-key\n";
echo "persist-tun\n";
echo "status openvpn-status.log\n";
echo "verb 3\n";
echo "explicit-exit-notify 1\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h3>Step 4: Start Server</h3>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; margin: 10px 0;'>\n";
echo "<pre style='background: #f1f3f4; padding: 10px; border-radius: 4px;'>\n";
echo "# Enable IP forwarding\n";
echo "echo 'net.ipv4.ip_forward = 1' | sudo tee -a /etc/sysctl.conf\n";
echo "sudo sysctl -p\n\n";
echo "# Start OpenVPN server\n";
echo "sudo systemctl start openvpn@server\n";
echo "sudo systemctl enable openvpn@server\n\n";
echo "# Check status\n";
echo "sudo systemctl status openvpn@server\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h2>Option 3: Quick Test Server (Temporary)</h2>\n";

echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107; margin: 10px 0;'>\n";
echo "<h4>⚡ Immediate Testing Solution</h4>\n";
echo "<p>For immediate testing, you can use a public OpenVPN test server:</p>\n";
echo "</div>\n";

// Add a button to update the database with a working test server
echo "<h3>Update Database with Working Test Server</h3>\n";
echo "<form method='post' style='margin: 20px 0;'>\n";
echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6;'>\n";
echo "<p>Click the button below to update the 'Working Test Server' with a functional configuration:</p>\n";
echo "<button type='submit' name='update_test_server' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;'>Update Test Server Configuration</button>\n";
echo "</div>\n";
echo "</form>\n";

// Handle the form submission
if (isset($_POST['update_test_server'])) {
    try {
        // Update the Working Test Server with a better configuration
        $newConfig = "client
dev tun
proto udp
remote ************* 1194
resolv-retry infinite
nobind
persist-key
persist-tun
auth SHA256
cipher AES-256-CBC
verb 3
mute 20
auth-nocache
script-security 2
fast-io
comp-lzo no
pull
route-delay 2
dhcp-option DNS *******
dhcp-option DNS *******
redirect-gateway def1 bypass-dhcp
keepalive 10 120";

        $updateSql = "UPDATE servers SET configFile = ? WHERE name = 'Working Test Server'";
        $stmt = $conn->prepare($updateSql);
        $stmt->bind_param('s', $newConfig);
        
        if ($stmt->execute()) {
            echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0;'>\n";
            echo "<h4>✅ Success!</h4>\n";
            echo "<p>Working Test Server configuration updated with local server IP (*************:1194)</p>\n";
            echo "<p><strong>Next:</strong> Set up the OpenVPN server using one of the options above, then test the Android app again.</p>\n";
            echo "</div>\n";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
            echo "<h4>❌ Error</h4>\n";
            echo "<p>Failed to update server configuration: " . $conn->error . "</p>\n";
            echo "</div>\n";
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 10px 0;'>\n";
        echo "<h4>❌ Error</h4>\n";
        echo "<p>Exception: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
}

echo "<h2>Testing Your Setup</h2>\n";

echo "<div style='background: #e7f3ff; padding: 15px; border-left: 4px solid #007cba; margin: 10px 0;'>\n";
echo "<h4>🧪 How to Test</h4>\n";
echo "<ol>\n";
echo "<li><strong>Set up OpenVPN server</strong> using one of the options above</li>\n";
echo "<li><strong>Update the database</strong> using the button above</li>\n";
echo "<li><strong>Test the Android app</strong> - it should now connect successfully</li>\n";
echo "<li><strong>Check traffic stats</strong> - both upload and download should increase</li>\n";
echo "<li><strong>Test internet browsing</strong> - websites should load through VPN</li>\n";
echo "</ol>\n";
echo "</div>\n";

echo "<p><strong>Navigation:</strong></p>\n";
echo "<ul>\n";
echo "<li><a href='validate_vpn_servers.php'>🔍 Validate Server Configurations</a></li>\n";
echo "<li><a href='test_vpn_connection_fixes.php'>🧪 Test API Endpoints</a></li>\n";
echo "<li><a href='vpn_fixes_summary.php'>📋 View Complete Fix Summary</a></li>\n";
echo "<li><a href='index.php'>← Back to Admin Panel</a></li>\n";
echo "</ul>\n";
?>
